# =============================================================================
# RK Institute Management System - Production Environment Variables
# =============================================================================
# 
# SECURITY NOTICE:
# - Never commit actual production values to version control
# - Use Vercel Environment Variables for production deployment
# - Rotate secrets regularly
# - Use strong, unique passwords and tokens
#
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Neon PostgreSQL Database URL
# Format: postgresql://username:password@host:port/database?sslmode=require
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# Database connection pool settings
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT Secret - Use a strong, random 256-bit key
# Generate with: openssl rand -base64 32
JWT_SECRET="your-super-secure-jwt-secret-key-here-256-bits"

# NextAuth.js Configuration
NEXTAUTH_URL="https://rk-institute.vercel.app"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# Session configuration
SESSION_TIMEOUT=86400
SESSION_SECURE=true

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Email Service (SendGrid)
SENDGRID_API_KEY="SG.your-sendgrid-api-key-here"
SENDGRID_FROM_EMAIL="<EMAIL>"
SENDGRID_FROM_NAME="RK Institute"

# SMS Service (Twilio) - Optional
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# File Storage (AWS S3 or Vercel Blob)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="rk-institute-files"

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Sentry Error Tracking
SENTRY_DSN="https://<EMAIL>/project-id"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="rk-institute"

# Vercel Analytics
VERCEL_ANALYTICS_ID="your-vercel-analytics-id"

# Google Analytics (Optional)
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY="pk_live_your-stripe-publishable-key"
STRIPE_SECRET_KEY="sk_live_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"

# Razorpay Configuration (for Indian market)
RAZORPAY_KEY_ID="rzp_live_your-razorpay-key"
RAZORPAY_KEY_SECRET="your-razorpay-secret"

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment
NODE_ENV="production"

# Application URLs
APP_URL="https://rk-institute.vercel.app"
API_URL="https://rk-institute.vercel.app/api"

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PAYMENTS=true
ENABLE_NOTIFICATIONS=true
ENABLE_FILE_UPLOADS=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=900000

# =============================================================================
# LOGGING & DEBUGGING
# =============================================================================

# Log Level (error, warn, info, debug)
LOG_LEVEL="info"

# Debug Mode (never enable in production)
DEBUG=false

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_FREQUENCY="daily"
BACKUP_RETENTION_DAYS=30

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="System maintenance in progress. Please try again later."

# =============================================================================
# PERFORMANCE & CACHING
# =============================================================================

# Redis Configuration (if using)
REDIS_URL="redis://your-redis-url:6379"
REDIS_PASSWORD="your-redis-password"

# Cache TTL (in seconds)
CACHE_TTL=3600

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Google Services
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Microsoft Services
MICROSOFT_CLIENT_ID="your-microsoft-client-id"
MICROSOFT_CLIENT_SECRET="your-microsoft-client-secret"

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# CORS Configuration
CORS_ORIGIN="https://rk-institute.vercel.app"
CORS_METHODS="GET,POST,PUT,DELETE,OPTIONS"

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Build Configuration
BUILD_TIMEOUT=600
BUILD_MEMORY=3008

# Vercel Configuration
VERCEL_PROJECT_ID="your-vercel-project-id"
VERCEL_ORG_ID="your-vercel-org-id"

# =============================================================================
# HEALTH CHECKS
# =============================================================================

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# NOTES FOR DEPLOYMENT
# =============================================================================
#
# 1. Set these variables in Vercel Dashboard:
#    - Go to Project Settings > Environment Variables
#    - Add each variable for Production environment
#    - Ensure sensitive values are properly secured
#
# 2. Required Variables for Basic Functionality:
#    - DATABASE_URL
#    - JWT_SECRET
#    - NEXTAUTH_URL
#    - NEXTAUTH_SECRET
#
# 3. Optional Variables (can be added later):
#    - Email service credentials
#    - Payment processing keys
#    - Analytics tracking IDs
#    - File storage credentials
#
# 4. Security Checklist:
#    - Use strong, unique passwords
#    - Enable 2FA on all service accounts
#    - Regularly rotate secrets
#    - Monitor access logs
#    - Use least privilege principle
#
# =============================================================================
