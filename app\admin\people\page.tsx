'use client';

import { useState } from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import {
  PeopleStatsOverview,
  PeopleQuickActions,
  PeopleModuleCards,
  PeopleRecentActivity,
  PeopleDataInsights,
  PeopleStats,
  QuickAction
} from '@/components/features/people-hub';

export default function PeopleHubPage() {
  const [stats, setStats] = useState<PeopleStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Quick actions configuration
  const quickActions: QuickAction[] = [
    {
      id: 'add-student',
      title: 'Add New Student',
      description: 'Enroll a new student in the institute',
      icon: '👨‍🎓',
      href: '/admin/students?action=add',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'add-family',
      title: 'Add New Family',
      description: 'Register a new family in the system',
      icon: '👨‍👩‍👧‍👦',
      href: '/admin/families?action=add',
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'add-user',
      title: 'Add New User',
      description: 'Create a new system user account',
      icon: '👤',
      href: '/admin/users?action=add',
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'bulk-import',
      title: 'Bulk Import',
      description: 'Import multiple records from spreadsheet',
      icon: '📊',
      href: '/admin/people/import',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  // Module cards configuration
  const moduleCards = [
    {
      id: 'students',
      title: 'Student Records',
      description: 'Manage student profiles, enrollment, and academic information',
      icon: '👨‍🎓',
      href: '/admin/students',
      color: 'from-blue-500 to-blue-600',
      stats: []
    },
    {
      id: 'families',
      title: 'Family Management',
      description: 'Manage family profiles, relationships, and contact information',
      icon: '👨‍👩‍👧‍👦',
      href: '/admin/families',
      color: 'from-green-500 to-green-600',
      stats: []
    },
    {
      id: 'users',
      title: 'User Accounts',
      description: 'Manage system users, roles, and access permissions',
      icon: '👤',
      href: '/admin/users',
      color: 'from-purple-500 to-purple-600',
      stats: []
    }
  ];

  return (
    <AdminLayout>
      {/* Data Management Component - handles all API calls and state */}
      <PeopleDataInsights
        onStatsUpdate={setStats}
        onLoadingChange={setLoading}
        onErrorChange={setError}
      />

      <div className="space-y-8">
        {/* Header and Stats Overview */}
        <PeopleStatsOverview
          stats={stats}
          loading={loading}
          error={error}
        />

        {/* Quick Actions Section */}
        <PeopleQuickActions actions={quickActions} />

        {/* Module Cards Section */}
        <PeopleModuleCards
          modules={moduleCards}
          stats={stats}
        />

        {/* Recent Activity Section */}
        <PeopleRecentActivity />
      </div>
    </AdminLayout>
  );
}
