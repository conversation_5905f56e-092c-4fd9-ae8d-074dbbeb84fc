# =============================================================================
# RK Institute Management System - Continuous Integration Pipeline
# =============================================================================
# This workflow runs on every push and pull request to ensure code quality
# and prevent breaking changes from being merged into main branches.

name: 🔄 Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

# Cancel in-progress workflows when a new commit is pushed
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # =============================================================================
  # Code Quality & Linting
  # =============================================================================
  lint:
    name: 🔍 Code Quality & Linting
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔍 Run ESLint
        run: npm run lint
        continue-on-error: false
        
      - name: 🎨 Check Prettier formatting
        run: npm run format:check
        continue-on-error: false

  # =============================================================================
  # TypeScript Compilation
  # =============================================================================
  typescript:
    name: 📝 TypeScript Compilation
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔧 Generate Prisma client
        run: npx prisma generate
        
      - name: 📝 TypeScript type checking
        run: npx tsc --noEmit

  # =============================================================================
  # Build Verification
  # =============================================================================
  build:
    name: 🏗️ Build Verification
    runs-on: ubuntu-latest
    needs: [lint, typescript]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔧 Generate Prisma client
        run: npx prisma generate
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 📊 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: .next/
          retention-days: 1

  # =============================================================================
  # Security Scanning
  # =============================================================================
  security:
    name: 🔒 Security Scanning
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔒 Run security audit
        run: npm audit --audit-level=high
        continue-on-error: true
        
      - name: 🔍 Run dependency vulnerability check
        run: npx audit-ci --config .audit-ci.json
        continue-on-error: true

  # =============================================================================
  # Test Execution (when tests are available)
  # =============================================================================
  test:
    name: 🧪 Test Execution
    runs-on: ubuntu-latest
    needs: [lint, typescript]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔧 Generate Prisma client
        run: npx prisma generate
        
      - name: 🧪 Run tests
        run: npm test
        continue-on-error: true
        env:
          NODE_ENV: test

  # =============================================================================
  # Final Status Check
  # =============================================================================
  ci-success:
    name: ✅ CI Success
    runs-on: ubuntu-latest
    needs: [lint, typescript, build, security, test]
    if: always()
    
    steps:
      - name: ✅ All checks passed
        if: ${{ needs.lint.result == 'success' && needs.typescript.result == 'success' && needs.build.result == 'success' }}
        run: |
          echo "🎉 All CI checks passed successfully!"
          echo "✅ Code quality: PASSED"
          echo "✅ TypeScript: PASSED" 
          echo "✅ Build: PASSED"
          echo "✅ Security: CHECKED"
          echo "✅ Tests: EXECUTED"
          
      - name: ❌ Some checks failed
        if: ${{ needs.lint.result != 'success' || needs.typescript.result != 'success' || needs.build.result != 'success' }}
        run: |
          echo "❌ Some CI checks failed!"
          echo "Lint: ${{ needs.lint.result }}"
          echo "TypeScript: ${{ needs.typescript.result }}"
          echo "Build: ${{ needs.build.result }}"
          exit 1
