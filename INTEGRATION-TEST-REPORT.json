{"timestamp": "2025-06-24T10:15:11.310Z", "summary": {"total": 77, "passed": 67, "failed": 10, "successRate": 87, "duration": 47}, "errors": [{"test": "operationsHub: OperationsHeader component loads", "details": "Component integration issue"}, {"test": "operationsHub: OperationsNavigation tabs functional", "details": "Component integration issue"}, {"test": "parentPortal: ParentDataInsights fetches data", "details": "Component integration issue"}, {"test": "Step 4: Admin sees payment update in Financial hub", "details": "Integration workflow issue"}, {"test": "Step 3: Parent views child academic reports", "details": "Integration workflow issue"}, {"test": "Header component render time", "details": "279ms exceeds 200ms target"}, {"test": "Navigation component render time", "details": "202ms exceeds 200ms target"}, {"test": "Stats overview component render time", "details": "300ms exceeds 200ms target"}, {"test": "Quick actions component render time", "details": "288ms exceeds 200ms target"}, {"test": "Alt text for images", "details": "Accessibility compliance issue"}], "performance": {"Landing page load time": {"value": 1157, "unit": "ms"}, "Admin dashboard load time": {"value": 1357, "unit": "ms"}, "Student portal load time": {"value": 1021, "unit": "ms"}, "Teacher portal load time": {"value": 1483, "unit": "ms"}, "Parent portal load time": {"value": 503, "unit": "ms"}, "Header component render time": {"value": 279, "unit": "ms"}, "Navigation component render time": {"value": 202, "unit": "ms"}, "Stats overview component render time": {"value": 300, "unit": "ms"}, "Quick actions component render time": {"value": 288, "unit": "ms"}, "User authentication API": {"value": 660, "unit": "ms"}, "Dashboard stats API": {"value": 378, "unit": "ms"}, "Academic data API": {"value": 798, "unit": "ms"}, "Financial data API": {"value": 643, "unit": "ms"}}}