# =============================================================================
# RK Institute Management System - Production Environment Configuration
# =============================================================================
# 
# SECURITY WARNING: 
# - Never commit this file with actual values to version control
# - Copy this file to .env.production and fill in your actual values
# - Ensure .env.production has restricted file permissions (600)
#
# =============================================================================

# -----------------------------------------------------------------------------
# DATABASE CONFIGURATION (REQUIRED)
# -----------------------------------------------------------------------------
# PostgreSQL connection string for production database
# Format: postgresql://username:password@host:port/database
DATABASE_URL="postgresql://rk_user:CHANGE_THIS_PASSWORD@localhost:5432/rk_institute_prod"

# Database connection pool settings
DATABASE_POOL_MIN="5"
DATABASE_POOL_MAX="20"
DATABASE_TIMEOUT="30000"

# -----------------------------------------------------------------------------
# SECURITY CONFIGURATION (REQUIRED)
# -----------------------------------------------------------------------------
# JWT secret key - MUST be at least 32 characters and cryptographically secure
# Generate with: openssl rand -base64 32
JWT_SECRET="CHANGE_THIS_TO_A_SECURE_32_PLUS_CHARACTER_SECRET_KEY"

# JWT token expiry time (4h recommended for production)
JWT_EXPIRY="4h"

# Bcrypt rounds for password hashing (12-14 recommended for production)
BCRYPT_ROUNDS="14"

# -----------------------------------------------------------------------------
# APPLICATION CONFIGURATION (REQUIRED)
# -----------------------------------------------------------------------------
# Your production domain URL (no trailing slash)
NEXT_PUBLIC_APP_URL="https://your-domain.com"

# Node environment (should be 'production')
NODE_ENV="production"

# Application port (3000 is default)
PORT="3000"

# -----------------------------------------------------------------------------
# RATE LIMITING CONFIGURATION
# -----------------------------------------------------------------------------
# Rate limiting window in milliseconds (15 minutes = 900000)
RATE_LIMIT_WINDOW="900000"

# Maximum requests per window
RATE_LIMIT_MAX="50"

# -----------------------------------------------------------------------------
# SECURITY HEADERS CONFIGURATION
# -----------------------------------------------------------------------------
# Enable security headers (recommended: true)
SECURITY_HEADERS_ENABLED="true"

# CORS origin (your domain)
CORS_ORIGIN="https://your-domain.com"

# -----------------------------------------------------------------------------
# EMAIL CONFIGURATION (OPTIONAL)
# -----------------------------------------------------------------------------
# SMTP server configuration for email notifications
# Format: smtp://username:password@host:port
EMAIL_SERVER="smtp://<EMAIL>:<EMAIL>:587"

# From email address for system emails
EMAIL_FROM="<EMAIL>"

# Enable/disable email functionality
EMAIL_ENABLED="true"

# -----------------------------------------------------------------------------
# SMS CONFIGURATION (OPTIONAL)
# -----------------------------------------------------------------------------
# SMS provider (currently supports 'twilio')
SMS_PROVIDER="twilio"

# Twilio API credentials
SMS_API_KEY="your-twilio-api-key"
SMS_API_SECRET="your-twilio-api-secret"
SMS_FROM_NUMBER="+**********"

# -----------------------------------------------------------------------------
# FILE UPLOAD CONFIGURATION
# -----------------------------------------------------------------------------
# Maximum file size in bytes (10MB = 10485760)
MAX_FILE_SIZE="10485760"

# Upload directory path
UPLOAD_PATH="/var/www/uploads"

# -----------------------------------------------------------------------------
# CACHING CONFIGURATION (OPTIONAL)
# -----------------------------------------------------------------------------
# Redis URL for caching (optional but recommended for production)
REDIS_URL="redis://localhost:6379"

# Enable/disable Redis caching
REDIS_ENABLED="true"

# -----------------------------------------------------------------------------
# MONITORING & LOGGING CONFIGURATION
# -----------------------------------------------------------------------------
# Log level (error, warn, info, debug)
LOG_LEVEL="warn"

# Enable audit logging
ENABLE_AUDIT_LOGS="true"

# -----------------------------------------------------------------------------
# BACKUP CONFIGURATION
# -----------------------------------------------------------------------------
# Enable automated backups
BACKUP_ENABLED="true"

# Backup schedule (cron format) - Daily at 2 AM
BACKUP_SCHEDULE="0 2 * * *"

# Backup retention in days
BACKUP_RETENTION_DAYS="30"

# Backup storage path
BACKUP_STORAGE_PATH="/var/backups/rk-institute"

# -----------------------------------------------------------------------------
# SSL/TLS CONFIGURATION
# -----------------------------------------------------------------------------
# Enable SSL (true for production)
SSL_ENABLED="true"

# SSL certificate paths (if using custom certificates)
SSL_CERT_PATH="/etc/ssl/certs/your-domain.crt"
SSL_KEY_PATH="/etc/ssl/private/your-domain.key"

# -----------------------------------------------------------------------------
# HEALTH CHECK CONFIGURATION
# -----------------------------------------------------------------------------
# Health check endpoint path
HEALTH_CHECK_URL="/api/health"

# Enable metrics collection
METRICS_ENABLED="true"

# -----------------------------------------------------------------------------
# ADDITIONAL SECURITY SETTINGS
# -----------------------------------------------------------------------------
# Session timeout in milliseconds (4 hours = ********)
SESSION_TIMEOUT="********"

# Maximum login attempts before lockout
MAX_LOGIN_ATTEMPTS="5"

# Account lockout time in milliseconds (15 minutes = 900000)
LOCKOUT_TIME="900000"

# =============================================================================
# DEPLOYMENT NOTES:
# =============================================================================
# 
# 1. Replace ALL placeholder values with your actual configuration
# 2. Ensure strong, unique passwords and secret keys
# 3. Use HTTPS in production (SSL_ENABLED="true")
# 4. Configure proper firewall rules
# 5. Set up monitoring and alerting
# 6. Regular security updates and backups
# 7. Test all configurations before going live
#
# =============================================================================
